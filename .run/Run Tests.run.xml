<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Run Tests" type="tests" factoryName="Autodetect">
    <module name="sm_td" />
    <option name="ENV_FILES" value="" />
    <option name="INTERPRETER_OPTIONS" value="" />
    <option name="PARENT_ENVS" value="true" />
    <option name="SDK_HOME" value="" />
    <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/tests" />
    <option name="IS_MODULE_SDK" value="true" />
    <option name="ADD_CONTENT_ROOTS" value="true" />
    <option name="ADD_SOURCE_ROOTS" value="true" />
    <option name="_new_additionalArguments" value="&quot;&quot;" />
    <option name="_new_target" value="&quot;$PROJECT_DIR$/tests/test_sm_td.py&quot;" />
    <option name="_new_targetType" value="&quot;PATH&quot;" />
    <method v="2" />
  </configuration>
</component>